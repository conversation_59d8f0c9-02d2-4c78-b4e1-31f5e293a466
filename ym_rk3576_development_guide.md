# 音曼科技 RK3576 Linux 系统开发指南

## 概述
本文档详细介绍了如何构建基于 RK3576 芯片的 Linux 系统，包括代码同步、编译环境配置和各个组件的构建过程。

## 1. 代码同步

### 1.1 从 YM 内部 GitLab 同步代码

请使用以下命令从音曼Gitlab服务器同步代码：

```bash
# 从 YM 内部 GitLab 初始化仓库
repo init --repo-url ssh://git@**************:2224/rk3576/repo.git \
    -u ssh://git@**************:2224/rk3576/manifests.git \
    -b ym_rk3576 \
    -m rk3576_ym_audio_linux6.1_release.xml

# 同步代码
.repo/repo/repo sync

# 创建并切换到工作分支
.repo/repo/repo start ym_rk3576 --all
```

**注意：** 使用 YM 内部源需要配置 SSH 密钥访问权限，外网需将IP地址和端口替换为：************:9060，即：ssh://git@************:9060/rk3576/repo.git，由于带宽限制，外网访问速度比较慢。

### 1.2 从本地代码tar包同步代码

为了减少外网同步代码时间，将预先下载好的代码tar包放在公司Windosws服务器，路径为\\***************\1hardware\芯片选型\主处理器\RK3576\rk3576_ym_audio_repo.tar.gz

将tar包下载到本地目录，解压后，进入rk3576_ym_audio目录，执行如下命令：

```bash
# Checkout代码
.repo/repo/repo sync -l

# 创建并切换到工作分支
.repo/repo/repo start ym_rk3576 --all
```

## 2. 系统构建

### 2.1 完整系统构建

构建完整的系统镜像，包括内核、根文件系统和 U-Boot，音曼产品配置文件为rockchip_rk3576_ym_audio_v10_defconfig：

```bash
# 配置构建目标
./build.sh lunch:rockchip_rk3576_ym_audio_v10_defconfig

# 开始构建
./build.sh
```

**注意：** 音曼Windows服务器存放了下载好的buildroot dl文件，可以将其拷贝到rk3576_linux6.1_release/buildroot/dl目录下，以加速构建过程，路径为\\***************\1hardware\芯片选型\主处理器\RK3576\dl.zip

### 2.2 根文件系统构建

如果只需要重新构建根文件系统：

```bash
./build.sh rootfs
```

### 2.3 内核构建

内核配置文件：rockchip_linux_defconfig，rk3576.config，DTS文件：rk3576-ym-audio-v10-linux.dts

#### 方法一：使用简化命令

```bash
# 配置内核
make ARCH=arm64 rockchip_linux_defconfig rk3576.config

# 构建内核镜像
make ARCH=arm64 rk3576-ym-audio-v10-linux.img -j24
```

#### 方法二：完整的交叉编译命令

```bash
# 配置内核（包含 RK3576 特定配置）
make CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- \
    ARCH=arm64 \
    rockchip_linux_defconfig \
    rk3576.config

# 构建内核镜像
make CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- \
    ARCH=arm64 \
    rk3576-ym-audio-v10-linux.img
```

### 2.4 U-Boot 构建

构建 U-Boot 引导加载程序：

```bash
./make.sh CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- rk3576 --spl-new
```

## 3. 构建环境要求

### 3.1 硬件要求
- 推荐使用多核处理器（命令中使用了 `-j24` 参数）
- 足够的磁盘空间（建议至少 100GB）
- 充足的内存（建议至少 8GB）

### 3.2 软件依赖
- Linux 开发环境（推荐 Ubuntu 20.04 或更高版本）
- Git 和 repo 工具
- 交叉编译工具链（已包含在代码仓库中）
- 必要的构建工具（make, gcc, python 等）

## 4. 目标平台信息

- **芯片平台：** RK3576
- **开发板：** EVB1 v1.0
- **架构：** ARM64
- **Linux 内核版本：** 6.1
- **配置文件：** rockchip_rk3576_ym_audio_v10_defconfig

## 5. 注意事项

1. **网络访问：** 确保能够访问相应的 Git 仓库
2. **权限配置：** 使用 SSH 方式同步代码时需要配置相应的访问权限
3. **路径设置：** 交叉编译工具链的路径可能需要根据实际安装位置调整
4. **并行编译：** 可以根据主机性能调整 `-j` 参数的数值
5. **清理构建：** 如果遇到构建问题，可能需要清理之前的构建产物

## 6. 故障排除

### 常见问题
- **同步失败：** 检查网络连接和仓库访问权限
- **编译错误：** 确认依赖包是否完整安装
- **路径问题：** 检查交叉编译工具链路径是否正确

### 获取帮助
遇到问题时，建议查看构建日志的详细输出，并根据错误信息进行相应的调试。